# Google Analytics 集成说明

## 🚀 快速设置

### 1. 获取 Google Analytics ID
1. 访问 [Google Analytics](https://analytics.google.com/)
2. 创建新的 GA4 属性
3. 获取测量 ID（格式：G-XXXXXXXXXX）

### 2. 配置环境变量
在 `.env.local` 文件中添加：
```bash
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### 3. 部署
部署到生产环境后，Google Analytics 将自动开始收集数据。

## 📊 跟踪的事件

### 自动跟踪
- **页面浏览量** - 所有页面访问
- **会话时长** - 用户停留时间
- **跳出率** - 单页面访问比例

### 自定义事件跟踪
- **prompt_optimization** - 提示词优化操作
  - 参数：模型类型、优化模式、提示词长度
- **example_click** - 示例提示词点击
  - 参数：示例文本、索引位置
- **copy_prompt** - 复制提示词操作
  - 参数：原始/优化版本
- **model_selection** - AI模型选择
  - 参数：选择的模型
- **mode_selection** - 优化模式选择
  - 参数：基础/详细模式
- **new_optimization** - 新建优化操作
- **history_open** - 历史记录查看

## 🔧 开发者使用

### 在组件中使用分析
```tsx
import { useAnalytics } from "@/hooks/use-analytics";

function MyComponent() {
  const analytics = useAnalytics();
  
  const handleClick = () => {
    analytics.trackEvent("button_click", "engagement", "my_button");
  };
  
  return <button onClick={handleClick}>Click me</button>;
}
```

### 跟踪自定义事件
```tsx
// 跟踪用户行为
analytics.trackPromptOptimization("chatgpt", "basic", 150);
analytics.trackExampleClick("Write me a marketing email", 0);
analytics.trackCopyPrompt("optimized");
```

## 📈 数据分析建议

### 关键指标
1. **转化率** - 访问者到实际使用工具的比例
2. **功能使用率** - 各个功能的使用频率
3. **用户路径** - 用户在网站上的行为流程
4. **模型偏好** - 用户最常选择的AI模型

### 优化建议
- 监控跳出率高的页面
- 分析用户最常用的功能
- 优化转化率低的流程
- 根据用户偏好调整界面

## 🔒 隐私说明

- 只在生产环境启用分析
- 不收集个人身份信息
- 遵循 GDPR 和其他隐私法规
- 用户可以通过浏览器设置禁用跟踪

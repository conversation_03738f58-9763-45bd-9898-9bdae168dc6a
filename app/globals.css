@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: #faf9f5;
  --foreground: #3d3929;
  --card: #faf9f5;
  --card-foreground: #141413;
  --popover: #ffffff;
  --popover-foreground: #28261b;
  --primary: #c96442;
  --primary-foreground: #ffffff;
  --secondary: #e9e6dc;
  --secondary-foreground: #535146;
  --muted: #ede9de;
  --muted-foreground: #83827d;
  --accent: #e9e6dc;
  --accent-foreground: #28261b;
  --destructive: #141413;
  --destructive-foreground: #ffffff;
  --border: #dad9d4;
  --input: #b4b2a7;
  --ring: #c96442;
  --chart-1: #b05730;
  --chart-2: #9c87f5;
  --chart-3: #ded8c4;
  --chart-4: #dbd3f0;
  --chart-5: #b4552d;
  --sidebar: #f5f4ee;
  --sidebar-foreground: #3d3d3a;
  --sidebar-primary: #c96442;
  --sidebar-primary-foreground: #fbfbfb;
  --sidebar-accent: #e9e6dc;
  --sidebar-accent-foreground: #343434;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #b5b5b5;
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: #262624;
  --foreground: #c3c0b6;
  --card: #262624;
  --card-foreground: #faf9f5;
  --popover: #30302e;
  --popover-foreground: #e5e5e2;
  --primary: #d97757;
  --primary-foreground: #ffffff;
  --secondary: #faf9f5;
  --secondary-foreground: #30302e;
  --muted: #1b1b19;
  --muted-foreground: #b7b5a9;
  --accent: #1a1915;
  --accent-foreground: #f5f4ee;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #3e3e38;
  --input: #52514a;
  --ring: #d97757;
  --chart-1: #b05730;
  --chart-2: #9c87f5;
  --chart-3: #1a1915;
  --chart-4: #2f2b48;
  --chart-5: #b4552d;
  --sidebar: #1f1e1d;
  --sidebar-foreground: #c3c0b6;
  --sidebar-primary: #343434;
  --sidebar-primary-foreground: #fbfbfb;
  --sidebar-accent: #0f0f0e;
  --sidebar-accent-foreground: #c3c0b6;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #b5b5b5;
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    content-visibility: auto;
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

:root {
  /* Tailwind CSS Candy cane */
  --pattern-fg: color-mix(in oklab, var(--color-foreground) 10%, transparent);
  & > * {
    @apply scrollbar-thin;
  }
}

@utility scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

@utility scrollbar-gutter-both {
  scrollbar-gutter: stable both-edges;
}

@utility grid-cols-fluid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
}

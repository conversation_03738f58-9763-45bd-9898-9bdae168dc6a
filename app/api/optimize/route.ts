export const runtime = 'edge';
import { NextRequest, NextResponse } from 'next/server';
import { type AIModel, type OptimizationMode } from '@/lib/prompt-optimizer';

// OpenAI API调用函数
async function callAI(userPrompt: string, targetModel: AIModel, mode: OptimizationMode) {
  const systemPrompt = `You are <PERSON><PERSON>, a master-level AI prompt optimization specialist. Your mission: transform any user input into precision-crafted prompts that unlock AI's full potential across all platforms.

## THE 4-D METHODOLOGY

### 1. DECONSTRUCT
- Extract core intent, key entities, and context
- Identify output requirements and constraints
- Map what's provided vs. what's missing

### 2. DIAGNOSE
- Audit for clarity gaps and ambiguity
- Check specificity and completeness
- Assess structure and complexity needs

### 3. DEVELOP
- Select optimal techniques based on request type:
  - **Creative** → Multi-perspective + tone emphasis
  - **Technical** → Constraint-based + precision focus
  - **Educational** → Few-shot examples + clear structure
  - **Complex** → Chain-of-thought + systematic frameworks
- Assign appropriate AI role/expertise
- Enhance context and implement logical structure

### 4. DELIVER
- Construct optimized prompt
- Format based on complexity
- Provide implementation guidance

## OPTIMIZATION TECHNIQUES

**Foundation:** Role assignment, context layering, output specs, task decomposition

**Advanced:** Chain-of-thought, few-shot learning, multi-perspective analysis, constraint optimization

**Platform Notes:**
- **ChatGPT/GPT-4:** Structured sections, conversation starters
- **Claude:** Longer context, reasoning frameworks
- **Gemini:** Creative tasks, comparative analysis
- **Others:** Apply universal best practices

## OPERATING MODES

**DETAIL MODE:** 
- Gather context with smart defaults
- Ask 2-3 targeted clarifying questions
- Provide comprehensive optimization

**BASIC MODE:**
- Quick fix primary issues
- Apply core techniques only
- Deliver ready-to-use prompt

## RESPONSE FORMATS

**Simple Requests:**
\`\`\`
**Your Optimized Prompt:**
[Improved prompt]

**What Changed:** [Key improvements]
\`\`\`

**Complex Requests:**
\`\`\`
**Your Optimized Prompt:**
[Improved prompt]

**Key Improvements:**
• [Primary changes and benefits]

**Techniques Applied:** [Brief mention]

**Pro Tip:** [Usage guidance]
\`\`\`

## WELCOME MESSAGE (REQUIRED)

When activated, display EXACTLY:

"Hello! I'm Lyra, your AI prompt optimizer. I transform vague requests into precise, effective prompts that deliver better results.

**What I need to know:**
- **Target AI:** ChatGPT, Claude, Gemini, or Other
- **Prompt Style:** DETAIL (I'll ask clarifying questions first) or BASIC (quick optimization)

**Examples:**
- "DETAIL using ChatGPT — Write me a marketing email"
- "BASIC using Claude — Help with my resume"

Just share your rough prompt and I'll handle the optimization!"

## PROCESSING FLOW

1. Auto-detect complexity:
   - Simple tasks → BASIC mode
   - Complex/professional → DETAIL mode
2. Inform user with override option
3. Execute chosen mode protocol
4. Deliver optimized prompt

**Memory Note:** Do not save any information from optimization sessions to memory.

Return ONLY a valid JSON response with this exact structure:
{
  "optimizedPrompt": "the improved prompt",
  "improvements": ["list of key improvements"],
  "techniques": ["techniques applied"],
  "proTip": "usage guidance (for detail mode only, null for basic)",
  "methodology": {
    "deconstruct": ["analysis points"],
    "diagnose": ["issues found"],
    "develop": ["solutions applied"],
    "deliver": ["final recommendations"]
  }
}`;

  // 检查是否有OpenAI API Key
  const apiKey = process.env.OPENAI_API_KEY;
  const baseUrl = process.env.OPENAI_API_BASE_URL;
  if (!apiKey) {
    // 如果没有API Key，返回简单的mock响应
    return {
      optimizedPrompt: `You are an expert ${targetModel} assistant specialized in high-quality responses.\n\nYour task: ${userPrompt}\n\nPlease provide a comprehensive and well-structured response.`,
      improvements: ["Added role assignment", "Enhanced clarity", "Structured task description"],
      techniques: ["Role assignment", "Context enhancement", "Task structuring"],
      proTip: mode === 'detail' ? `Optimized for ${targetModel} capabilities` : undefined,
      methodology: {
        deconstruct: [`Analyzed: ${userPrompt}`, `Target: ${targetModel}`, `Mode: ${mode}`],
        diagnose: ["Identified optimization opportunities", "Assessed clarity needs"],
        develop: [`Applied ${mode} mode techniques`, "Enhanced with role assignment"],
        deliver: ["Created optimized prompt", "Added platform-specific guidance"]
      }
    };
  }

  try {
    // 调用OpenAI API
    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini', // 使用更经济的模型
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: `Please optimize this prompt for ${targetModel} in ${mode} mode: "${userPrompt}"`
          }
        ],
        temperature: 0.7,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    // 解析JSON响应
    const result = JSON.parse(content);

    // 验证响应格式
    if (!result.optimizedPrompt || !result.improvements || !result.techniques) {
      throw new Error('Invalid response format from OpenAI');
    }

    return result;

  } catch (error) {
    console.error('OpenAI API error:', error);

    // 如果API调用失败，返回fallback响应
    return {
      optimizedPrompt: `You are an expert ${targetModel} assistant specialized in high-quality responses.\n\nYour task: ${userPrompt}\n\nPlease provide a comprehensive and well-structured response.`,
      improvements: ["Added role assignment", "Enhanced clarity", "Structured task description"],
      techniques: ["Role assignment", "Context enhancement", "Task structuring"],
      proTip: mode === 'detail' ? `Optimized for ${targetModel} capabilities` : undefined,
      methodology: {
        deconstruct: [`Analyzed: ${userPrompt}`, `Target: ${targetModel}`, `Mode: ${mode}`],
        diagnose: ["Identified optimization opportunities", "Assessed clarity needs"],
        develop: [`Applied ${mode} mode techniques`, "Enhanced with role assignment"],
        deliver: ["Created optimized prompt", "Added platform-specific guidance"]
      }
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json.call(request);
    const { prompt, model, mode } = body;

    // 验证输入
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      return NextResponse.json(
        { error: 'Prompt is required and must be a non-empty string' },
        { status: 400 }
      );
    }

    if (!model || !['chatgpt', 'claude', 'gemini', 'other'].includes(model)) {
      return NextResponse.json(
        { error: 'Valid model is required (chatgpt, claude, gemini, or other)' },
        { status: 400 }
      );
    }

    if (!mode || !['basic', 'detail'].includes(mode)) {
      return NextResponse.json(
        { error: 'Valid mode is required (basic or detail)' },
        { status: 400 }
      );
    }

    // 调用AI进行优化
    const result = await callAI(
      prompt.trim(),
      model as AIModel,
      mode as OptimizationMode
    );

    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error during optimization' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Lyra Prompt Optimization API',
    version: '2.0.0',
    description: 'AI prompt optimization using Lyra 4-D methodology',
    endpoints: {
      POST: '/api/optimize - Optimize a prompt with specified model and mode'
    },
    supportedModels: ['chatgpt', 'claude', 'gemini', 'other'],
    supportedModes: ['basic', 'detail'],
    methodology: '4-D: Deconstruct, Diagnose, Develop, Deliver'
  });
}

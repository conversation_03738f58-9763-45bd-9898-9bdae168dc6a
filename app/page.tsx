export const runtime = "edge";
import { ContainerWrapper } from "@/components/wrappers";
import { PageHeader } from "@/components/page-header";
import { Footer } from "@/components/layouts/footer";
import { Separator } from "@/components/ui/separator";
import { HomeClient } from "@/components/home-client";
import { seoConfig } from "@/lib/seo-config";

// SEO metadata for homepage
export const metadata = seoConfig.home;

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Header */}
      <ContainerWrapper withCane className="@container">
        <PageHeader />
      </ContainerWrapper>

      <Separator />
      <div className="relative pointer-events-none gap-0">
        <ContainerWrapper withCane>
          <div className="absolute inset-0 z-[-1] size-full bg-[image:repeating-linear-gradient(315deg,_var(--pattern-fg)_0,_var(--pattern-fg)_1px,_transparent_0,_transparent_50%)] bg-[size:10px_10px]" />
          <div className="h-10"></div>
        </ContainerWrapper>
        <Separator />
      </div>
      <ContainerWrapper withCane>
        {/* Main Layout - Left/Right Split */}
        <HomeClient />
      </ContainerWrapper>
      <Separator />
      <ContainerWrapper withCane>
        {/* Footer */}
        <Footer />
      </ContainerWrapper>
    </div>
  );
}

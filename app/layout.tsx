import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Script from "next/script";
// import {
//   CustomizerSidebar,
//   CustomizerSidebarToggle,
// } from "@/components/layouts/custom-sidebar";
import { SidebarInset } from "@/components/ui/sidebar";
import { ContainerWrapper } from "@/components/wrappers";
import { ModeSwitcher } from "@/components/mode-switcher";
import {
  MainNavigation,
  MobileNavigation,
} from "@/components/layouts/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Providers } from "@/components/providers";
import { GoogleAnalytics } from "@/components/analytics/google-analytics";

const geistSans = Nunito({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Lyra Prompt - AI Prompt Directory & Generator",
  description:
    "Discover AI prompts & create your own with Lyra Prompt. Browse prompts for text, images, videos & coding. Works with ChatGPT, Claude, Gemini.",
  keywords:
    "lyra prompt, ai prompt directory, prompt directory, prompt generator, chatgpt prompts, claude prompts, gemini prompts, ai prompts, prompt engineering, text prompts, image prompts, video prompts, coding prompts",
  openGraph: {
    title: "Lyra Prompt - AI Prompt Directory & Generator",
    description:
      "Discover AI prompts & create your own with Lyra Prompt. Browse prompts for text, images, videos & coding. Works with ChatGPT, Claude, Gemini.",
    type: "website",
    url: "https://lyraprompt.com",
    images: [
      {
        url: "https://lyraprompt.com/og-image.webp",
        alt: "Lyra Prompt Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Lyra Prompt - AI Prompt Directory & Generator",
    description:
      "Discover AI prompts & create your own with Lyra Prompt. Browse prompts for text, images, videos & coding. Works with ChatGPT, Claude, Gemini.",
  },
};

// const SIDEBAR_WIDTH = "21rem";
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script
          defer
          data-domain="lyraprompt.com"
          src="https://app.insightbi.net/js/script.js"
        ></Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {/* Google Analytics */}
        {process.env.NEXT_PUBLIC_GA_ID && (
          <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID} />
        )}

        <Providers>
          {/* <SidebarProvider
            defaultOpen={false}
            style={{
              "--sidebar-width": SIDEBAR_WIDTH,
            }}
          >
            <CustomizerSidebar variant="inset" /> */}

          <SidebarInset className="relative isolate max-h-svh overflow-hidden peer-data-[variant=inset]:max-h-[calc(100svh-1rem)]">
            <header className="isolate z-20 flex shrink-0 items-center gap-2 border-b md:z-10">
              <ContainerWrapper className="flex items-center justify-between">
                <div className="flex h-16 w-full items-center">
                  {/* <div className="inline-flex">
                      <CustomizerSidebarToggle />
                    </div> */}

                  <MainNavigation />
                </div>

                <div className="flex items-center justify-center">
                  <ModeSwitcher />

                  <MobileNavigation />
                </div>
              </ContainerWrapper>
            </header>

            <ScrollArea className="relative z-10 flex h-full flex-col overflow-hidden">
              {children}
            </ScrollArea>
          </SidebarInset>
          {/* </SidebarProvider> */}
        </Providers>
      </body>
    </html>
  );
}

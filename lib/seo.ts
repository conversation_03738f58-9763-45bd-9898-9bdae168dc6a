import config from "@/config/base";
import type { Metada<PERSON> } from "next";

export function generateSEOMetadata(
  path: string,
  title: string,
  description: string
): Metadata {
  const baseUrl = config.baseUrl;
  const currentUrl = new URL(path, baseUrl).href;
  const canonicalUrl = new URL(path, baseUrl).href;

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      type: "website",
      url: currentUrl,
      images: [
        {
          url: "https://lyraprompt.com/og-image.webp",
          alt: "Lyra Prompt Logo",
        },
      ],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
      },
      nocache: false,
    },
  };
}

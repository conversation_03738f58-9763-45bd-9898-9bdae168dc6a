import { LucideIcon } from "lucide-react";

type Badge = "Soon" | "New";
export type NavLink = {
  href: string;
  title: string;
  icon?: LucideIcon;
  badge?: Badge;
};

export const NAV_LINKS: NavLink[] = [
  // Future navigation links for prompt categories
  // { href: "/text-prompts", title: "Text Prompts", badge: "Soon" },
  // { href: "/image-prompts", title: "Image Prompts", badge: "Soon" },
  // { href: "/video-prompts", title: "Video Prompts", badge: "Soon" },
  // { href: "/coding-prompts", title: "Coding Prompts", badge: "Soon" },
];
import { generateSEOMetadata } from "./seo";

// SEO configuration for different pages
export const seoConfig = {
  // Homepage SEO - optimized for "lyra prompt" and related keywords
  home: generateSEOMetadata(
    "/",
    "Lyra Prompt - AI Prompt Generator & Directory",
    "Discover AI prompts & create your own. Browse Lyra prompts for text, images, videos & coding. Works with ChatGPT, Claude, Gemini & all AI models."
  ),

  // Future page configurations
  textPrompts: generateSEOMetadata(
    "/text-prompts",
    "Text Prompts - Lyra Prompt Directory for Writing & Content",
    "Discover optimized text prompts for writing, content creation & marketing. Lyra prompt directory for ChatGPT, Claude & all AI models."
  ),

  imagePrompts: generateSEOMetadata(
    "/image-prompts",
    "Image Prompts - Lyra Prompt Directory for AI Art Generation",
    "Create stunning AI images with optimized prompts. Lyra prompt directory for Midjourney, DALL-E, Stable Diffusion & all AI image generators."
  ),

  videoPrompts: generateSEOMetadata(
    "/video-prompts",
    "Video Prompts - Lyra Prompt Directory for AI Video Creation",
    "Generate engaging video content with optimized prompts. Lyra prompt directory for AI video generators, script writing & content creation."
  ),

  codingPrompts: generateSEOMetadata(
    "/coding-prompts",
    "Coding Prompts - Lyra Prompt Directory for Programming",
    "Boost your coding with optimized programming prompts. Lyra prompt directory for code generation, debugging & software development tasks."
  ),
};

// Keywords for different categories
export const seoKeywords = {
  primary: [
    "lyra prompt",
    "ai prompt directory",
    "prompt directory",
    "ai prompt generator",
    "prompt optimizer",
    "ai prompts",
    "prompt engineering"
  ],
  
  textPrompts: [
    "text prompts",
    "writing prompts", 
    "content prompts",
    "chatgpt prompts",
    "claude prompts"
  ],
  
  imagePrompts: [
    "image prompts",
    "ai image prompts",
    "midjourney prompts", 
    "dall-e prompts",
    "stable diffusion prompts"
  ],
  
  videoPrompts: [
    "video prompts",
    "ai video prompts",
    "video script prompts",
    "video content prompts"
  ],
  
  codingPrompts: [
    "coding prompts",
    "programming prompts",
    "code generation prompts",
    "development prompts"
  ]
};

// Schema.org structured data for homepage
export const homePageSchema = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Lyra Prompt",
  "description": "AI prompt generator and optimizer for text, image, video, and coding prompts",
  "url": "https://lyraprompt.com",
  "applicationCategory": "AI Tools",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "creator": {
    "@type": "Organization", 
    "name": "Lyra Prompt"
  }
};

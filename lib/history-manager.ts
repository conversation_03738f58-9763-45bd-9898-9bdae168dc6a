import { OptimizationResult } from './prompt-optimizer';

export interface HistoryItem {
  id: string;
  timestamp: number;
  originalPrompt: string;
  optimizedPrompt: string;
  model: string;
  mode: string;
  improvements: string[];
  techniques: string[];
  methodology: OptimizationResult['methodology'];
  proTip?: string;
  isFavorite: boolean;
}

// 常量定义
const STORAGE_KEY = 'lyra_optimization_history';
const MAX_HISTORY_ITEMS = 50;

// 获取历史记录
export function getHistory(): HistoryItem[] {
  if (typeof window === 'undefined') return [];

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];

    const history = JSON.parse(stored) as HistoryItem[];
    return history.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    // Silently handle error to avoid Cloudflare Edge Runtime issues
    return [];
  }
}

// 添加新的优化记录
export function addToHistory(
  originalPrompt: string,
  result: OptimizationResult,
  model: string,
  mode: string
): HistoryItem {
  const newItem: HistoryItem = {
    id: generateId(),
    timestamp: Date.now(),
    originalPrompt,
    optimizedPrompt: result.optimizedPrompt,
    model,
    mode,
    improvements: result.improvements,
    techniques: result.techniques,
    methodology: result.methodology,
    proTip: result.proTip,
    isFavorite: false
  };

  const history = getHistory();
  history.unshift(newItem);

  // 限制历史记录数量
  if (history.length > MAX_HISTORY_ITEMS) {
    history.splice(MAX_HISTORY_ITEMS);
  }

  saveHistory(history);
  return newItem;
}

// 删除历史记录项
export function deleteItem(id: string): boolean {
  const history = getHistory();
  const index = history.findIndex(h => h.id === id);

  if (index !== -1) {
    history.splice(index, 1);
    saveHistory(history);
    return true;
  }

  return false;
}

// 清空所有历史记录
export function clearHistory(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(STORAGE_KEY);
  }
}

// 保存历史记录
function saveHistory(history: HistoryItem[]): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      // Silently handle error to avoid Cloudflare Edge Runtime issues
    }
  }
}

// 生成唯一ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
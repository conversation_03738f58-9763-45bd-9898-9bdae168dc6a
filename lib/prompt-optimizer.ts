export type AIModel = "chatgpt" | "claude" | "gemini" | "other";
export type OptimizationMode = "basic" | "detail";
export type RequestType = "creative" | "technical" | "educational" | "complex" | "general";

export interface OptimizationResult {
  optimizedPrompt: string;
  improvements: string[];
  techniques: string[];
  proTip?: string;
  methodology: {
    deconstruct: string[];
    diagnose: string[];
    develop: string[];
    deliver: string[];
  };
}

export interface PromptAnalysis {
  coreIntent: string;
  keyEntities: string[];
  context: string;
  outputRequirements: string[];
  constraints: string[];
  missingElements: string[];
  clarityGaps: string[];
  specificityLevel: "low" | "medium" | "high";
  complexityLevel: "simple" | "moderate" | "complex";
  requestType: RequestType;
}







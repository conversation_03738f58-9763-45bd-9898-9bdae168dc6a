import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>rk<PERSON>,
  Co<PERSON>,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  Check,
} from "lucide-react";
import {
  type OptimizationResult,
  type AIModel,
  type OptimizationMode,
} from "@/lib/prompt-optimizer";
import { BorderCard } from "@/components/ui/border-card";
import { FrameHighlight } from "./frame-highlight";

interface OptimizationResultsProps {
  result: OptimizationResult;
  selectedModel: AIModel;
  mode: OptimizationMode;
  onCopyToClipboard: (text: string) => void;
  onNewOptimization: () => void;
}

export function OptimizationResults({
  result,
  selectedModel,
  mode,
  onCopyToClipboard,
  onNewOptimization,
}: OptimizationResultsProps) {
  const [copied, setCopied] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const handleCopy = () => {
    onCopyToClipboard(result.optimizedPrompt);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <BorderCard>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Your Optimized Prompt</h3>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {mode.toUpperCase()}
            </Badge>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Use this prompt with confidence to get the best results from{" "}
          <FrameHighlight>
            <b>{selectedModel}</b>
          </FrameHighlight>
        </p>
        {/* Optimized Prompt Display */}
        <div className="relative">
          <div className="bg-muted/50 rounded-lg p-4 border border-border/50">
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {result.optimizedPrompt}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap items-center gap-2 mt-3">
            <Button
              size="sm"
              onClick={handleCopy}
              className="flex items-center gap-2"
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  Copy Prompt
                </>
              )}
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={onNewOptimization}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              New Optimization
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center gap-2"
            >
              {showDetails ? (
                <>
                  <ChevronUp className="h-4 w-4" />
                  Hide Details
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4" />
                  Show Details
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Details Section - Collapsible */}
        {showDetails && (
          <div className="space-y-4 border-t border-border/50 pt-4">
            {/* Key Improvements */}
            <div>
              <h4 className="font-medium mb-3 text-sm">Key Improvements:</h4>
              <div className="space-y-2">
                {result.improvements.map((improvement, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0"></div>
                    <span className="text-sm text-muted-foreground">
                      {improvement}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Techniques Applied */}
            {result.techniques.length > 0 && (
              <div>
                <h4 className="font-medium mb-3 text-sm">
                  Techniques Applied:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {result.techniques.map((technique, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {technique}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Pro Tip */}
            {result.proTip && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2 text-sm">
                  💡 Pro Tip:
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {result.proTip}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </BorderCard>
  );
}

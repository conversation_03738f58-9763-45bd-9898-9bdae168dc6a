"use client";

import { useState, useEffect, useRef } from "react";
import {
  type AIModel,
  type OptimizationMode,
  type OptimizationResult,
} from "@/lib/prompt-optimizer";

import { addToHistory, type HistoryItem } from "@/lib/history-manager";
import { ChatInputForm } from "@/components/chat-input-form";
import { OptimizationResults } from "@/components/optimization-results";

import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Info, X } from "lucide-react";
import { cn } from "@/lib/utils";

import { BorderCard } from "@/components/ui/border-card";
import { LyraMarkdownDemo } from "@/components/lyra-markdown-demo";
import { Separator } from "@/components/ui/separator";
import { useAnalytics } from "@/hooks/use-analytics";

export function HomeClient() {
  const [inputPrompt, setInputPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState<AIModel>("chatgpt");
  const [mode, setMode] = useState<OptimizationMode>("basic");
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [result, setResult] = useState<OptimizationResult | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [selectedExampleIndex, setSelectedExampleIndex] = useState(-1);

  // State for dynamic input fields
  const [inputs, setInputs] = useState<Record<string, string>>({});

  // Analytics
  const analytics = useAnalytics();

  // Define the agent with input fields
  const selectedAgent = {
    name: "AI Prompt Generator",
    inputFields: [
      {
        name: "prompt",
        label: "Your Prompt",
        type: "textarea" as const,
        placeholder:
          "Enter your rough idea or prompt... (Press Enter to generate & optimize, Shift+Enter for new line)",
      },
    ],
  };

  // Handle input changes for dynamic fields
  const handleInputChange = (fieldName: string, value: string) => {
    setInputs((prev) => ({
      ...prev,
      [fieldName]: value,
    }));

    // Also update the main inputPrompt for backward compatibility
    if (fieldName === "prompt") {
      setInputPrompt(value);
    }
  };

  // Auto-scroll to bottom when new messages appear
  useEffect(() => {
    // Only scroll if there's a result or currently optimizing
    // Don't scroll on initial load when inputPrompt is empty
    if (result || isOptimizing) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [result, isOptimizing]);

  const handleOptimize = async () => {
    // Use the prompt from inputs or fallback to inputPrompt
    const promptToOptimize = inputs.prompt || inputPrompt;
    if (!promptToOptimize.trim()) return;

    setIsOptimizing(true);

    try {
      // 调用API进行优化
      const response = await fetch("/api/optimize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: promptToOptimize,
          model: selectedModel,
          mode: mode,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // 特殊处理API key错误
        if (errorData.error?.includes("OpenAI API key is required")) {
          setResult({
            optimizedPrompt: "⚠️ OpenAI API Key Required",
            improvements: [
              "Lyra requires OpenAI API access to provide professional optimization",
              "Please set up your OpenAI API key to unlock the complete 4-D methodology",
              "Without API access, only basic rule-based optimization is available",
            ],
            techniques: ["Configuration Required"],
            methodology: {
              deconstruct: [
                "API key missing - cannot access Lyra's full capabilities",
              ],
              diagnose: [
                "OpenAI API key not configured in environment variables",
              ],
              develop: ["Setup required: Add OPENAI_API_KEY to your .env file"],
              deliver: [
                "Complete setup to access professional Lyra optimization",
              ],
            },
            proTip:
              "Get your API key from https://platform.openai.com/api-keys and add it to your .env file as OPENAI_API_KEY=your_key_here",
          });
          setIsOptimizing(false);
          return;
        }

        throw new Error(errorData.error || "Optimization failed");
      }

      const optimizationResult = await response.json();
      setResult(optimizationResult);

      // Track successful optimization
      analytics.trackPromptOptimization(selectedModel, mode, promptToOptimize.length);

      // 保存到历史记录
      addToHistory(
        promptToOptimize,
        optimizationResult,
        selectedModel,
        mode
      );
    } catch (error) {
      // Silently handle error
      // 如果优化失败，显示错误信息
      setResult({
        optimizedPrompt: "Sorry, optimization failed. Please try again.",
        improvements: ["Error occurred during optimization"],
        techniques: [],
        methodology: {
          deconstruct: ["Failed to analyze prompt"],
          diagnose: ["Error in diagnosis"],
          develop: ["Could not develop optimization"],
          deliver: ["Failed to deliver result"],
        },
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      // Fix for Cloudflare Edge Runtime - bind navigator.clipboard methods properly
      if (typeof navigator !== 'undefined' && navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText.call(navigator.clipboard, text);
      } else {
        // Fallback for environments without clipboard API
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
      // Track copy action
      analytics.trackCopyPrompt(text === inputPrompt ? "original" : "optimized");
    } catch (error) {
      // Silently handle clipboard error
    }
  };

  const handleNewOptimization = () => {
    setResult(null);
    setInputPrompt("");
    setInputs({});
    // Track new optimization
    analytics.trackNewOptimization();
  };

  // 从历史记录中加载结果
  const loadHistoryItem = (item: HistoryItem) => {
    setInputPrompt(item.originalPrompt);
    setInputs({
      prompt: item.originalPrompt,
    });
    setSelectedModel(item.model as AIModel);
    setMode(item.mode as OptimizationMode);
    setResult({
      optimizedPrompt: item.optimizedPrompt,
      improvements: item.improvements,
      techniques: item.techniques,
      methodology: item.methodology,
      proTip: item.proTip,
    });
  };

  const examplePrompts = [
    "Generate a marketing email for SaaS product",
    "Create a realistic portrait image prompt",
    "Write a Python function for data analysis",
    "Design a video script for product demo",
    "Build a creative writing story prompt",
    "Generate SEO blog content outline",
  ];

  const onExampleSelect = (prompt: string, index: number) => {
    setInputPrompt(prompt);
    setInputs((prev) => ({
      ...prev,
      prompt: prompt,
    }));
    setSelectedExampleIndex(index);
    // Track example click
    analytics.trackExampleClick(prompt, index);
  };
  
  const resetState = () => {
    setInputPrompt("");
    setInputs({});
    setSelectedExampleIndex(-1);
  };

  return (
    <div className="flex flex-col lg:flex-row py-4 md:py-6 gap-4 lg:gap-6">
      {/* Left Panel - Chat Interface Style */}
      {/* Chat Messages Area */}
      <div className="w-full lg:w-1/2 space-y-3 md:space-y-2">
        {/* Welcome Message */}
        <BorderCard>
          <div className="space-y-4">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold text-card-foreground mb-2">
                👋 Welcome to Lyra Prompt Generator
              </h2>
              <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">
                Transform any idea into powerful AI prompts. Our advanced generator creates optimized prompts
                for text, images, videos, and coding using proven optimization techniques.
              </p>
            </div>
          </div>
        </BorderCard>
        <BorderCard>
          <div className="flex-none space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h2 className="text-xs sm:text-sm font-medium">
                  Try These Examples
                </h2>
                <Tooltip delayDuration={300}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 hover:bg-neutral-100 transition-colors duration-200"
                    >
                      <Info className="h-3 sm:h-3.5 w-3 sm:w-3.5 text-neutral-500" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    align="end"
                    className="max-w-[200px] sm:max-w-[250px] text-[11px] sm:text-xs p-2 sm:p-3"
                  >
                    <p className="leading-relaxed">
                      Try these example prompts to see how our generator optimizes them for different AI models and use cases.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div
                className="relative w-full"
                key={`${selectedExampleIndex}-examples`}
              >
                <div
                  key={`${selectedExampleIndex}-examples-container`}
                  className="flex flex-wrap gap-2 md:gap-2"
                >
                  {examplePrompts.map((example, index) => (
                    <Button
                      size="sm"
                      key={`${selectedExampleIndex}-${index}`}
                      className={cn(
                        "min-w-fit whitespace-nowrap text-xs py-1.5 px-3 sm:py-1 sm:px-2.5 bg-secondary text-neutral-800 transition-all duration-200 hover:bg-neutral-50 active:scale-95 touch-manipulation relative group",
                        index === selectedExampleIndex &&
                          "bg-black text-white hover:bg-black/90 shadow-[0px_1px_2px_0px_rgba(38,_99,_235,_0.3),_0px_1px_1px_0px_rgba(38,_99,_235,_0.1)_inset,_0px_0px_0px_1px_rgba(38,_99,_235,_0.4)_inset] hover:shadow-[0px_2px_4px_0px_rgba(38,_99,_235,_0.25),_0px_1px_1px_0px_rgba(38,_99,_235,_0.1)_inset,_0px_0px_0px_1px_rgba(38,_99,_235,_0.4)_inset] pr-7"
                      )}
                      onClick={() => {
                        if (index === selectedExampleIndex) {
                          onExampleSelect(example, -1);
                          resetState();
                        } else {
                          onExampleSelect(example, index);
                        }
                      }}
                    >
                      {example}
                      {index === selectedExampleIndex && (
                        <div
                          className="absolute right-1.5 top-1 -translate-y-1/2 p-0.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors duration-200 cursor-pointer backdrop-blur-[2px] shadow-[0px_1px_1px_0px_rgba(0,_0,_0,_0.2)]"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onExampleSelect(example, -1);
                            resetState();
                          }}
                        >
                          <X className="h-3 w-3" strokeWidth={2.5} />
                        </div>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
            <Separator />
            {/* Chat Input Area */}
            <ChatInputForm
              selectedAgent={selectedAgent}
              inputs={inputs}
              onInputChange={handleInputChange}
              inputPrompt={inputPrompt}
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              mode={mode}
              setMode={setMode}
              isOptimizing={isOptimizing}
              onOptimize={handleOptimize}
              onSelectPrompt={(prompt) => {
                setInputPrompt(prompt);
                setInputs((prev) => ({
                  ...prev,
                  prompt: prompt,
                }));
              }}
              onSelectResult={loadHistoryItem}
            />
          </div>
        </BorderCard>
      </div>
      {/* Right Panel - Details & History */}
      <div className="w-full lg:w-1/2">
        <div className="space-y-3 md:space-y-2">
          {!result && <LyraMarkdownDemo />}
          {!result && (
            <BorderCard>
              <div className="space-y-4">
                <h2 className="text-lg sm:text-xl font-semibold text-card-foreground mb-2">
                  Prompt Analysis & Optimization
                </h2>
                <p className="text-sm sm:text-base text-muted-foreground leading-relaxed mb-4">
                  Submit any prompt for instant optimization. Our generator analyzes and improves prompts
                  for text generation, image creation, video content, or coding tasks:
                </p>
                <div className="space-y-3 text-sm text-muted-foreground">
                  <div className="flex items-center gap-3">
                    <span className="text-sm">🔍</span>
                    <span>Complete 4-D methodology breakdown</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm">⚡</span>
                    <span>AI model-specific optimization techniques</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm">💡</span>
                    <span>Detailed improvement suggestions</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm">🎯</span>
                    <span>Pro tips for maximum effectiveness</span>
                  </div>
                </div>
              </div>
            </BorderCard>
          )}

          {/* Detailed Results */}
          {result && (
            <OptimizationResults
              result={result}
              selectedModel={selectedModel}
              mode={mode}
              onCopyToClipboard={copyToClipboard}
              onNewOptimization={handleNewOptimization}
            />
          )}
        </div>
      </div>
      <div ref={messagesEndRef} />
    </div>
  );
}

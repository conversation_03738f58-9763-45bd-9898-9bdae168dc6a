"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { History, Trash2, Copy } from "lucide-react";
import {
  getHistory,
  deleteItem as deleteHistoryItem,
  clearHistory as clearHistoryData,
  type HistoryItem,
} from "@/lib/history-manager";

interface HistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectPrompt: (prompt: string) => void;
  onSelectResult: (item: HistoryItem) => void;
}

export function HistoryModal({
  open,
  onOpenChange,
  onSelectPrompt,
  onSelectResult,
}: HistoryModalProps) {
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  // 加载数据
  useEffect(() => {
    if (open) {
      loadData();
    }
  }, [open]);

  const loadData = () => {
    setHistory(getHistory());
  };

  // 删除项目
  const deleteItem = (id: string) => {
    if (confirm("Are you sure you want to delete this optimization?")) {
      deleteHistoryItem(id);
      loadData();
    }
  };

  // 清空历史记录
  const clearHistory = () => {
    if (
      confirm(
        "Are you sure you want to clear all history? This cannot be undone."
      )
    ) {
      clearHistoryData();
      loadData();
    }
  };

  // 处理选择结果并关闭模态框
  const handleSelectResult = (item: HistoryItem) => {
    onSelectResult(item);
    onOpenChange(false);
  };

  // 处理选择提示并关闭模态框
  const handleSelectPrompt = (prompt: string) => {
    onSelectPrompt(prompt);
    onOpenChange(false);
  };

  // 复制优化后的提示到剪贴板（不关闭模态框）
  const handleCopyOptimized = (item: HistoryItem, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("Copying optimized prompt:", item.optimizedPrompt); // 调试日志
    navigator.clipboard
      .writeText(item.optimizedPrompt)
      .then(() => {
        console.log("Copy successful"); // 调试日志
        setCopiedId(item.id);
        setTimeout(() => {
          setCopiedId(null);
        }, 2000);
      })
      .catch((err) => {
        console.error("Copy failed:", err); // 调试日志
      });
  };

  // 渲染历史项目
  const renderHistoryItem = (item: HistoryItem) => (
    <div
      key={item.id}
      className="border rounded-lg p-4 space-y-3 hover:bg-muted/50 transition-colors"
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-3">
            <Badge variant="outline" className="text-xs">
              {item.model}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {item.mode.toUpperCase()}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {new Date(item.timestamp).toLocaleDateString()}
            </span>
          </div>

          {/* Original Prompt */}
          <div className="mb-3">
            <div className="text-xs font-medium text-foreground mb-1">
              Original:
            </div>
            <p className="text-sm text-muted-foreground line-clamp-2 italic">
              {item.originalPrompt}
            </p>
          </div>

          {/* Optimized Prompt */}
          <div>
            <div className="text-xs font-medium text-foreground mb-1">
              Optimized:
            </div>
            <p className="text-sm text-muted-foreground line-clamp-3  italic">
              {item.optimizedPrompt}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-1 ml-4">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 text-primary hover:text-primary/80"
            onClick={(e) => handleCopyOptimized(item, e)}
            title={
              copiedId === item.id
                ? "Copied!"
                : "Copy optimized prompt to clipboard"
            }
          >
            <Copy
              className={`h-4 w-4 ${
                copiedId === item.id ? "text-green-500" : ""
              }`}
            />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
            onClick={(e) => {
              e.stopPropagation();
              deleteItem(item.id);
            }}
            title="Delete this optimization"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Optimization History
          </DialogTitle>
          <DialogDescription>
            View and manage your previous prompt optimizations
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-hidden">
          {/* Search and Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={clearHistory}
              disabled={history.length === 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* History List */}
          <div className="space-y-3 overflow-y-auto flex-1 pr-2 max-h-[50vh]">
            {history.length > 0 ? (
              history.map(renderHistoryItem)
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <History className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <p className="text-lg font-medium mb-2">
                  No optimization history yet
                </p>
                <p className="text-sm">
                  Start optimizing prompts to see your history here
                </p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

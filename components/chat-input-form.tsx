import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Check, Zap } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { type AIModel, type OptimizationMode } from "@/lib/prompt-optimizer";
import { BorderCard } from "@/components/ui/border-card";
import { HistoryModal } from "@/components/history-modal";
import { type HistoryItem } from "@/lib/history-manager";

interface InputField {
  name: string;
  label: string;
  type: "text" | "textarea";
  placeholder: string;
}

interface Agent {
  name: string;
  inputFields: InputField[];
}

interface ChatInputFormProps {
  selectedAgent: Agent;
  inputs: Record<string, string>;
  onInputChange: (fieldName: string, value: string) => void;
  inputPrompt: string;
  selectedModel: AIModel;
  setSelectedModel: (model: AIModel) => void;
  mode: OptimizationMode;
  setMode: (mode: OptimizationMode) => void;
  isOptimizing: boolean;
  onOptimize: () => void;
  onSelectPrompt: (prompt: string) => void;
  onSelectResult: (item: HistoryItem) => void;
}

export function ChatInputForm({
  selectedAgent,
  inputs,
  onInputChange,
  inputPrompt,
  selectedModel,
  setSelectedModel,
  mode,
  setMode,
  isOptimizing,
  onOptimize,
  onSelectPrompt,
  onSelectResult,
}: ChatInputFormProps) {
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const handleKeyDown = (e: React.KeyboardEvent, fieldName: string) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      const currentValue = inputs[fieldName] || inputPrompt;
      if (currentValue.trim() && !isOptimizing) {
        onOptimize();
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* Configuration */}
      <div className="space-y-3">
        <div>
          <Label className="text-xs font-medium  mb-2 block">
            ✨ Target AI
          </Label>
          <div className="flex gap-1">
            {[
              {
                value: "chatgpt",
                label: "ChatGPT",
                tooltip:
                  "OpenAI's GPT models - Great for general tasks and creative writing",
                iconSrc: "/logos/openai.svg",
              },
              {
                value: "claude",
                label: "Claude",
                tooltip:
                  "Anthropic's Claude - Excellent for analysis and reasoning",
                iconSrc: "/logos/claude-color.svg",
              },
              {
                value: "gemini",
                label: "Gemini",
                tooltip:
                  "Google's Gemini - Strong in multimodal and technical tasks",
                iconSrc: "/logos/gemini-color.svg",
              },
              {
                value: "other",
                label: "Other",
                tooltip: "Other AI models - Generic optimization approach",
              },
            ].map((model) => (
              <Button
                size="sm"
                key={model.value}
                onClick={() => setSelectedModel(model.value as AIModel)}
                className={cn(
                  "flex items-center gap-1.5 px-2.5 py-1.5 rounded-md text-xs font-medium transition-all duration-200",
                  selectedModel === model.value
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground hover:bg-muted/80"
                )}
              >
                {model.iconSrc && (
                  <Image
                    src={model.iconSrc}
                    alt={`${model.label} icon`}
                    width={16}
                    height={16}
                    className="h-4 w-4"
                  />
                )}
                {model.label}
              </Button>
            ))}
          </div>
        </div>
        <div>
          <Label className="text-xs font-medium mb-2 block">⚡ Mode</Label>
          <div className="flex gap-1">
            {[
              {
                value: "basic",
                label: "BASIC",
                tooltip:
                  "Quick optimization with essential improvements and core techniques",
              },
              {
                value: "detail",
                label: "DETAIL",
                tooltip:
                  "Comprehensive analysis with detailed 4-D methodology breakdown",
              },
            ].map((modeOption) => (
              <Tooltip key={modeOption.value} delayDuration={300}>
                <TooltipTrigger asChild>
                  <button
                    onClick={() =>
                      setMode(modeOption.value as OptimizationMode)
                    }
                    className={cn(
                      "px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200",
                      mode === modeOption.value
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground hover:bg-muted/80"
                    )}
                  >
                    {modeOption.label}
                  </button>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-[200px] text-xs">
                  {modeOption.tooltip}
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        </div>
      </div>

      <BorderCard variant="simple" className="p-2">
        {/* Input Fields */}
        <div className="space-y-5" key={selectedAgent.name}>
          {selectedAgent.inputFields.map((field, index) => (
            <div key={index} className="space-y-2.5">
              <div className="flex items-center gap-1.5">
                <Label
                  className={cn(
                    "text-xs font-medium transition-all duration-200"
                  )}
                >
                  {field.label}
                </Label>
                {inputs[field.name]?.trim() && (
                  <div className="flex items-center justify-center h-4 w-4 rounded-full bg-green-500/10 ring-1 ring-green-500/30">
                    <Check className="h-2.5 w-2.5 text-green-500 stroke-[3]" />
                  </div>
                )}
              </div>
              {field.type === "textarea" ? (
                <div className="relative p-1 rounded-[9px] group shadow-[0px_1px_1px_0px_rgba(0,_0,_0,_0.05),_0px_1px_1px_0px_rgba(255,_252,_240,_0.5)_inset,_0px_0px_0px_1px_hsla(0,_0%,_100%,_0.1)_inset,_0px_0px_1px_0px_rgba(28,_27,_26,_0.5)]">
                  <Textarea
                    value={inputs[field.name] || ""}
                    onChange={(e) => onInputChange(field.name, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(e, field.name)}
                    placeholder={field.placeholder}
                    disabled={isOptimizing}
                    className={cn(
                      "rounded-[5px] resize-none text-[16px] leading-snug md:leading-relaxed md:text-[14px] caret-blue-400 border-none ring-1 ring-[#F6F6F6] ring-offset-neutral-50 ring-offset-1 transition-all duration-200 shadow-[0px_1px_0px_0px_hsla(0,_0%,_0%,_0.02)_inset,_0px_0px_0px_1px_hsla(0,_0%,_0%,_0.02)_inset,_0px_0px_0px_1px_rgba(255,_255,_255,_0.25)] focus-visible:ring-[#2B7BE5] focus-visible:ring-[1px] focus-visible:ring-offset-blue-100 focus-visible:ring-offset-2 ease-out",
                      "min-h-[340px] lg:min-h-[300px]"
                    )}
                  />
                  <div className="absolute bottom-3 right-3 text-[11px] text-neutral-400 bg-white/80 px-1.5 py-0.5 rounded-[4px] opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-[0px_1px_1px_0px_rgba(0,_0,_0,_0.05),_0px_1px_1px_0px_rgba(255,_252,_240,_0.5)_inset,_0px_0px_0px_1px_hsla(0,_0%,_100%,_0.1)_inset,_0px_0px_1px_0px_rgba(28,_27,_26,_0.5)]">
                    {(inputs[field.name] || "").length} chars
                  </div>
                </div>
              ) : null}
            </div>
          ))}

          {/* Bottom Action Bar */}
          <div className="flex items-center gap-2 pt-4 flex-wrap">
            {/* Clear/Trash Button */}

            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                // Clear input functionality
                Object.keys(inputs).forEach((key) => {
                  onInputChange(key, "");
                });
              }}
            >
              <DeletePutBackIcon className="h-4 w-4" />
            </Button>

            {/* History Button */}
            <Button
              variant="outline"
              onClick={() => setIsHistoryModalOpen(true)}
            >
              <WorkHistoryIcon className="h-4 w-4" />
              History
            </Button>

            {/* Run Agent Button - Main CTA */}
            <Button
              onClick={onOptimize}
              disabled={!inputPrompt.trim() || isOptimizing}
              className="flex-1"
            >
              {isOptimizing ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  Run Optimize Prompt
                </>
              )}
            </Button>
          </div>
        </div>
      </BorderCard>

      {/* History Modal */}
      <HistoryModal
        open={isHistoryModalOpen}
        onOpenChange={setIsHistoryModalOpen}
        onSelectPrompt={onSelectPrompt}
        onSelectResult={onSelectResult}
      />
    </div>
  );
}

const WorkHistoryIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width={24}
    height={24}
    color={"#000000"}
    fill={"none"}
    {...props}
  >
    <path
      opacity="0.4"
      d="M9.60546 5.5H13.4082C16.9934 5.5 18.7861 5.5 19.8999 6.63496C20.7568 7.50819 20.9544 8.7909 21 11V13C21 13.6016 21 14.1551 20.9952 14.6655C20.1702 13.6493 18.9109 13 17.5 13C15.0147 13 13 15.0147 13 17.5C13 18.9109 13.6493 20.1702 14.6655 20.9952C14.1551 21 13.6015 21 13 21H9.60546C6.02021 21 4.22759 21 3.11379 19.865C2 18.7301 2 16.9034 2 13.25C2 9.59661 2 7.76992 3.11379 6.63496C4.22759 5.5 6.02021 5.5 9.60546 5.5Z"
      fill="currentColor"
    />
    <path
      d="M11.0065 21H9.60546C6.02021 21 4.22759 21 3.11379 19.865C2 18.7301 2 16.9034 2 13.25C2 9.59661 2 7.76992 3.11379 6.63496C4.22759 5.5 6.02021 5.5 9.60546 5.5H13.4082C16.9934 5.5 18.7861 5.5 19.8999 6.63496C20.7568 7.50819 20.9544 8.7909 21 11"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.85 18.85L17.5 17.95V15.7M13 17.5C13 19.9853 15.0147 22 17.5 22C19.9853 22 22 19.9853 22 17.5C22 15.0147 19.9853 13 17.5 13C15.0147 13 13 15.0147 13 17.5Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 5.5L15.9007 5.19094C15.4056 3.65089 15.1581 2.88087 14.5689 2.44043C13.9796 2 13.197 2 11.6316 2H11.3684C9.80304 2 9.02036 2 8.43111 2.44043C7.84186 2.88087 7.59436 3.65089 7.09934 5.19094L7 5.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const DeletePutBackIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width={24}
    height={24}
    color={"#000000"}
    fill={"none"}
    {...props}
  >
    <path
      opacity="0.4"
      d="M19.5 5.5L18.8803 15.5251C18.7219 18.0864 18.6428 19.3671 18.0008 20.2879C17.6833 20.7431 17.2747 21.1273 16.8007 21.416C15.8421 22 14.559 22 11.9927 22C9.42312 22 8.1383 22 7.17905 21.4149C6.7048 21.1257 6.296 20.7408 5.97868 20.2848C5.33688 19.3626 5.25945 18.0801 5.10461 15.5152L4.5 5.5H19.5Z"
      fill="currentColor"
    />
    <path
      d="M4.5 5.5L5.08671 15.1781C5.26178 18.066 5.34932 19.5099 6.14772 20.5018C6.38232 20.7932 6.65676 21.0505 6.96304 21.2662C8.00537 22 9.45801 22 12.3633 22H15.9867C17.4593 22 18.7162 20.9398 18.9583 19.4932C19.2643 17.6646 17.8483 16 15.9867 16H13.0357"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M14.5 18.5C13.9943 18.0085 12 16.7002 12 16C12 15.2998 13.9943 13.9915 14.5 13.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 5.5H3"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M16.0575 5.5L15.3748 4.09173C14.9213 3.15626 14.6946 2.68852 14.3035 2.39681C14.2167 2.3321 14.1249 2.27454 14.0288 2.2247C13.5957 2 13.0759 2 12.0363 2C10.9706 2 10.4377 2 9.99745 2.23412C9.89986 2.28601 9.80675 2.3459 9.71906 2.41317C9.3234 2.7167 9.10239 3.20155 8.66037 4.17126L8.05469 5.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M19 13.5L19.5 5.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

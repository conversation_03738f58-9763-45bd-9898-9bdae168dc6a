import * as React from "react"
import { cn } from "@/lib/utils"

interface BorderCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode
  variant?: "default" | "simple"
}

const BorderCard = React.forwardRef<HTMLDivElement, BorderCardProps>(
  ({ className, children, variant = "default", ...props }, ref) => {
    if (variant === "simple") {
      return (
        <div
          ref={ref}
          className={cn(
            "rounded-lg bg-card border border-border p-4 sm:p-5 md:p-4",
            className
          )}
          {...props}
        >
          {children}
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid grid-cols-1 rounded-lg sm:rounded-xl md:rounded-[2rem] p-1 sm:p-1.5 md:p-2 shadow-md bg-gradient-to-br from-background/50 to-muted/30",
          className
        )}
        {...props}
      >
        <div className="rounded-md sm:rounded-lg md:rounded-3xl bg-card p-3 sm:p-4 md:p-5 shadow-xl ring-1 ring-border/20">
          <div className="w-full h-full overflow-hidden">
            <div className="flex flex-col h-full">
              {children}
            </div>
          </div>
        </div>
      </div>
    )
  }
)

BorderCard.displayName = "BorderCard"

export { BorderCard }
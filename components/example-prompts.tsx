"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Lightbulb, Copy } from "lucide-react";

interface ExamplePrompt {
  title: string;
  prompt: string;
  category: "creative" | "technical" | "educational" | "business";
  difficulty: "basic" | "intermediate" | "advanced";
}

const EXAMPLE_PROMPTS: ExamplePrompt[] = [
  {
    title: "Marketing Email",
    prompt: "Write me a marketing email",
    category: "business",
    difficulty: "basic"
  },
  {
    title: "Code Review",
    prompt: "Help me review this JavaScript function for performance issues",
    category: "technical",
    difficulty: "intermediate"
  },
  {
    title: "Learning Plan",
    prompt: "Create a study plan for learning machine learning",
    category: "educational",
    difficulty: "intermediate"
  },
  {
    title: "Creative Story",
    prompt: "Write a short story about time travel",
    category: "creative",
    difficulty: "basic"
  },
  {
    title: "API Documentation",
    prompt: "Document this REST API endpoint with examples",
    category: "technical",
    difficulty: "advanced"
  },
  {
    title: "Business Strategy",
    prompt: "Analyze market opportunities for a SaaS startup",
    category: "business",
    difficulty: "advanced"
  }
];

interface ExamplePromptsProps {
  onSelectPrompt: (prompt: string) => void;
}

export function ExamplePrompts({ onSelectPrompt }: ExamplePromptsProps) {
  const getCategoryColor = (category: string) => {
    const colors = {
      creative: "bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300",
      technical: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
      educational: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
      business: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
    };
    return colors[category as keyof typeof colors] || colors.business;
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      basic: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      intermediate: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
      advanced: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
    };
    return colors[difficulty as keyof typeof colors] || colors.basic;
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          Example Prompts
        </CardTitle>
        <CardDescription>
          Try these example prompts to see how Lyra optimizes different types of requests
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {EXAMPLE_PROMPTS.map((example, index) => (
            <div
              key={index}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer group"
              onClick={() => onSelectPrompt(example.prompt)}
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-sm group-hover:text-purple-600 transition-colors">
                  {example.title}
                </h4>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectPrompt(example.prompt);
                  }}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                "{example.prompt}"
              </p>
              
              <div className="flex gap-2">
                <Badge 
                  variant="secondary" 
                  className={`text-xs ${getCategoryColor(example.category)}`}
                >
                  {example.category}
                </Badge>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getDifficultyColor(example.difficulty)}`}
                >
                  {example.difficulty}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

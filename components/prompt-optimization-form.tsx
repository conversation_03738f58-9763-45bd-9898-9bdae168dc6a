import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Brain, Target, Zap } from "lucide-react";
import { type AIModel, type OptimizationMode } from "@/lib/prompt-optimizer";

interface PromptOptimizationFormProps {
  inputPrompt: string;
  setInputPrompt: (prompt: string) => void;
  selectedModel: AIModel;
  setSelectedModel: (model: AIModel) => void;
  mode: OptimizationMode;
  setMode: (mode: OptimizationMode) => void;
  isOptimizing: boolean;
  onOptimize: () => void;
}

export function PromptOptimizationForm({
  inputPrompt,
  setInputPrompt,
  selectedModel,
  setSelectedModel,
  mode,
  setMode,
  isOptimizing,
  onOptimize,
}: PromptOptimizationFormProps) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Prompt Optimization
        </CardTitle>
        <CardDescription>
          Enter your rough prompt and I&apos;ll optimize it using the 4-D methodology
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Target AI Platform</label>
            <Select value={selectedModel} onValueChange={(value: AIModel) => setSelectedModel(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="chatgpt">ChatGPT / GPT-4</SelectItem>
                <SelectItem value="claude">Claude</SelectItem>
                <SelectItem value="gemini">Gemini</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Optimization Mode</label>
            <Select value={mode} onValueChange={(value: OptimizationMode) => setMode(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">BASIC - Quick optimization</SelectItem>
                <SelectItem value="detail">DETAIL - Comprehensive analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Your Prompt</label>
          <Textarea
            placeholder="Enter your rough prompt here... (e.g., 'Write me a marketing email')"
            value={inputPrompt}
            onChange={(e) => setInputPrompt(e.target.value)}
            className="min-h-[120px]"
          />
        </div>

        {/* Action Button */}
        <Button
          onClick={onOptimize}
          disabled={!inputPrompt.trim() || isOptimizing}
          className="w-full"
          size="lg"
        >
          {isOptimizing ? (
            <>
              <Zap className="h-4 w-4 mr-2 animate-spin" />
              Optimizing with 4-D Methodology...
            </>
          ) : (
            <>
              <Target className="h-4 w-4 mr-2" />
              Optimize Prompt
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

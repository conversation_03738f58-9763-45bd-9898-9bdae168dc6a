"use client";

import Image from "next/image";
import { ContainerWrapper } from "@/components/wrappers";

export function Footer() {
  return (
    <>
      <footer className="bg-background py-8">
        <ContainerWrapper>
          <div className="flex flex-col  space-y-4">
            {/* Logo/Brand */}
            <div className="flex items-center space-x-2">
              <Image
                src="/lyra-logo.svg"
                alt="Lyra Logo"
                width={25}
                height={25}
              />
              <span className="text-base font-bold">Lyra Prompt</span>
            </div>

            {/* Description */}
            <p className="text-xs text-muted-foreground max-w-lg">
              Lyra Prompt - Your comprehensive AI prompt directory and generator. Browse thousands of prompts
              or create your own for text, images, videos, and coding. Works with ChatGPT, Claude, Gemini, and all AI models.
            </p>

            {/* Copyright */}
            <p className="text-xs text-muted-foreground">
              © {new Date().getFullYear()} Lyra Prompt. All rights reserved.
            </p>
          </div>
        </ContainerWrapper>
      </footer>
    </>
  );
}

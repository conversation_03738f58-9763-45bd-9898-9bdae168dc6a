import { TextGif } from "./ui/TextGif";
import { FrameHighlight } from "./frame-highlight";

export function PageHeader() {
  return (
    <div className="py-6 md:py-8">
      <TextGif
        gifUrl="https://media.giphy.com/media/v1.Y2lkPWVjZjA1ZTQ3OWQ1dG0wb29meXZ5dnhwNWMyOHMwZWxhMHkxem95aXgxcjR2ZWhjMCZlcD12MV9naWZzX3JlbGF0ZWQmY3Q9Zw/gQvJdypeqrEyhZ9lzn/giphy.gif"
        text="Lyra Prompt"
        size="md"
        weight="bold"
      />
      <p className="text-sm sm:text-base text-muted-foreground mb-1">
        <FrameHighlight>AI Prompt Generator & Optimizer</FrameHighlight>
      </p>
      <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">
        Transform your ideas into powerful AI prompts with our advanced generator.
        Create and optimize prompts for text, images, videos, and coding tasks using our proven 4-D methodology.
      </p>
    </div>
  );
}

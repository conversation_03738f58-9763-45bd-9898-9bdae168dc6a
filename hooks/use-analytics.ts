"use client";

import { useGoogleAnalytics } from "@/components/analytics/google-analytics";

export const useAnalytics = () => {
  const { trackEvent, trackPageView } = useGoogleAnalytics();

  // Track prompt optimization events
  const trackPromptOptimization = (
    model: string,
    mode: string,
    promptLength: number
  ) => {
    trackEvent("prompt_optimization", "engagement", `${model}_${mode}`, promptLength);
  };

  // Track example prompt clicks
  const trackExampleClick = (exampleText: string, index: number) => {
    trackEvent("example_click", "engagement", exampleText, index);
  };

  // Track copy to clipboard
  const trackCopyPrompt = (promptType: "original" | "optimized") => {
    trackEvent("copy_prompt", "engagement", promptType);
  };

  // Track model selection
  const trackModelSelection = (model: string) => {
    trackEvent("model_selection", "user_preference", model);
  };

  // Track mode selection
  const trackModeSelection = (mode: string) => {
    trackEvent("mode_selection", "user_preference", mode);
  };

  // Track new optimization button
  const trackNewOptimization = () => {
    trackEvent("new_optimization", "engagement", "reset");
  };

  // Track history modal open
  const trackHistoryOpen = () => {
    trackEvent("history_open", "engagement", "modal");
  };

  // Track page views
  const trackPage = (pageName: string) => {
    trackPageView(window.location.href, pageName);
  };

  return {
    trackPromptOptimization,
    trackExampleClick,
    trackCopyPrompt,
    trackModelSelection,
    trackModeSelection,
    trackNewOptimization,
    trackHistoryOpen,
    trackPage,
  };
};

# Lyra - AI Prompt Optimization Specialist

Transform any user input into precision-crafted prompts that unlock AI's full potential across all platforms using the 4-D methodology.

## Getting Started

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Setup (Optional)

For AI-enhanced optimization, copy the example environment file and add your OpenAI API key:

```bash
cp .env.example .env
```

Edit `.env` and add your OpenAI API key:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

**Note:** The app works without an API key using intelligent fallback responses. The OpenAI integration provides enhanced optimization when available.

### 3. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3002](http://localhost:3002) with your browser to see the result.

## API Usage

### POST /api/optimize

Optimize a prompt using <PERSON>yra's 4-D methodology:

```bash
curl -X POST http://localhost:3002/api/optimize \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a marketing email",
    "model": "chatgpt",
    "mode": "detail"
  }'
```

**Parameters:**
- `prompt` (string): The prompt to optimize
- `model` (string): Target AI model (`chatgpt`, `claude`, `gemini`, `other`)
- `mode` (string): Optimization mode (`basic`, `detail`)

**Response:**
```json
{
  "optimizedPrompt": "You are an expert marketing specialist...",
  "improvements": ["Added role assignment", "Enhanced clarity"],
  "techniques": ["Role assignment", "Context enhancement"],
  "proTip": "For better results with ChatGPT...",
  "methodology": {
    "deconstruct": ["Analysis points"],
    "diagnose": ["Issues found"],
    "develop": ["Solutions applied"],
    "deliver": ["Final recommendations"]
  }
}
```

## The 4-D Methodology

1. **DECONSTRUCT** - Extract core intent, key entities, and context
2. **DIAGNOSE** - Audit for clarity gaps and ambiguity
3. **DEVELOP** - Select optimal techniques based on request type
4. **DELIVER** - Construct optimized prompt with platform-specific guidance

## Features

- ✅ **Function-based Architecture** - Clean, maintainable code
- ✅ **OpenAI Integration** - Real AI-powered optimization
- ✅ **Fallback Support** - Works without API keys
- ✅ **Multi-platform** - Optimized for ChatGPT, Claude, Gemini
- ✅ **Two Modes** - Basic (quick) and Detail (comprehensive)
- ✅ **Edge Runtime** - Fast, serverless deployment

## Tech Stack

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **OpenAI API** - AI-powered optimization
- **Edge Runtime** - Serverless deployment

## Deployment

```bash
npm run build
npm start
```

Or deploy to Vercel with one click:

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/lyra-prompt)
